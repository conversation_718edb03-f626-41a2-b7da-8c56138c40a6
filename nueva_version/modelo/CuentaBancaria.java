public abstract class CuentaBancaria {

    private int numero;
    private int saldo;
    private String descripcion;

   public CuentaBancaria (int numeroCuenta, int saldo, String descripcion){
    this.numero = numeroCuenta;
    this.saldo = 0;
    this.descripcion = descripcion;
   }

   public int getNumeroCuenta(){
    return numeroCuenta;
   }

   public int getSaldo(){
    return saldo;
   }

   public String getDescripcion(){
    return descripcion;
   }


   public void setSaldo(int saldo){
    this.saldo = saldo;
   }

   public void setDescripcion(String descripcion){
    this.descripcion = descripcion;
   }

   public abstract boolean puedeTransferir();

  
}